"""
用户反馈服务层

提供反馈相关的业务逻辑处理
"""
from typing import Optional, List, Dict, Any, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import select, func, and_, or_, desc, asc

from app.services.base import BaseService
from app.models.feedback import Feedback, FeedbackStatus
from app.models.user import User
from app.schemas.feedback import (
    FeedbackCreate, FeedbackUpdate, FeedbackListQuery, FeedbackStatsResponse
)
import logging

logger = logging.getLogger(__name__)


class FeedbackService(BaseService[Feedback, FeedbackCreate, FeedbackUpdate]):
    """反馈服务类"""
    
    def __init__(self):
        super().__init__(Feedback)
    
    def create_feedback(
        self, 
        db: Session, 
        feedback_data: FeedbackCreate,
        current_user_id: Optional[int] = None
    ) -> Feedback:
        """
        创建反馈
        
        Args:
            db: 数据库会话
            feedback_data: 反馈数据
            current_user_id: 当前用户ID（可选，支持匿名反馈）
            
        Returns:
            创建的反馈对象
        """
        try:
            # 如果提供了current_user_id，优先使用它
            user_id = current_user_id if current_user_id else feedback_data.user_id
            
            feedback = Feedback(
                content=feedback_data.content,
                user_id=user_id,
                status=FeedbackStatus.PENDING.value
            )
            
            db.add(feedback)
            db.commit()
            db.refresh(feedback)
            
            logger.info(f"创建反馈成功 ID={feedback.id}, user_id={user_id}")
            return feedback
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建反馈失败: {str(e)}")
            raise
    
    def get_feedback_list(
        self, 
        db: Session, 
        query_params: FeedbackListQuery,
        include_user_info: bool = True
    ) -> Tuple[List[Feedback], int]:
        """
        获取反馈列表（分页）
        
        Args:
            db: 数据库会话
            query_params: 查询参数
            include_user_info: 是否包含用户信息
            
        Returns:
            (反馈列表, 总数量)
        """
        try:
            # 构建基础查询
            query = select(Feedback)
            count_query = select(func.count(Feedback.id))
            
            # 添加筛选条件
            conditions = []
            
            if query_params.status:
                conditions.append(Feedback.status == query_params.status.value)
            
            if query_params.user_id:
                conditions.append(Feedback.user_id == query_params.user_id)
            
            if conditions:
                query = query.where(and_(*conditions))
                count_query = count_query.where(and_(*conditions))
            
            # 如果需要用户信息，添加join
            if include_user_info:
                query = query.outerjoin(User, Feedback.user_id == User.id)
            
            # 添加排序
            order_column = getattr(Feedback, query_params.order_by, Feedback.created_at)
            if query_params.order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
            
            # 添加分页
            offset = (query_params.page - 1) * query_params.page_size
            query = query.offset(offset).limit(query_params.page_size)
            
            # 执行查询
            result = db.execute(query)
            feedbacks = result.scalars().all()
            
            # 获取总数
            total_result = db.execute(count_query)
            total = total_result.scalar()
            
            logger.info(f"获取反馈列表成功，共{total}条记录，当前页{query_params.page}")
            return feedbacks, total
            
        except Exception as e:
            logger.error(f"获取反馈列表失败: {str(e)}")
            raise
    
    def update_feedback(
        self, 
        db: Session, 
        feedback_id: int, 
        update_data: FeedbackUpdate
    ) -> Optional[Feedback]:
        """
        更新反馈（管理员操作）
        
        Args:
            db: 数据库会话
            feedback_id: 反馈ID
            update_data: 更新数据
            
        Returns:
            更新后的反馈对象
        """
        try:
            feedback = self.get(db, feedback_id)
            if not feedback:
                logger.warning(f"反馈不存在 ID={feedback_id}")
                return None
            
            # 更新字段
            if update_data.status is not None:
                feedback.status = update_data.status.value
            
            if update_data.admin_reply is not None:
                feedback.admin_reply = update_data.admin_reply
            
            db.commit()
            db.refresh(feedback)
            
            logger.info(f"更新反馈成功 ID={feedback_id}")
            return feedback
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新反馈失败 ID={feedback_id}: {str(e)}")
            raise
    
    def get_feedback_stats(self, db: Session) -> FeedbackStatsResponse:
        """
        获取反馈统计信息
        
        Args:
            db: 数据库会话
            
        Returns:
            反馈统计信息
        """
        try:
            # 总数统计
            total_count = db.execute(select(func.count(Feedback.id))).scalar()
            
            # 按状态统计
            pending_count = db.execute(
                select(func.count(Feedback.id)).where(Feedback.status == FeedbackStatus.PENDING.value)
            ).scalar()
            
            processing_count = db.execute(
                select(func.count(Feedback.id)).where(Feedback.status == FeedbackStatus.PROCESSING.value)
            ).scalar()
            
            resolved_count = db.execute(
                select(func.count(Feedback.id)).where(Feedback.status == FeedbackStatus.RESOLVED.value)
            ).scalar()
            
            ignored_count = db.execute(
                select(func.count(Feedback.id)).where(Feedback.status == FeedbackStatus.IGNORED.value)
            ).scalar()
            
            # 时间范围统计
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time())
            
            week_start = today_start - timedelta(days=today.weekday())
            
            today_count = db.execute(
                select(func.count(Feedback.id)).where(Feedback.created_at >= today_start)
            ).scalar()
            
            week_count = db.execute(
                select(func.count(Feedback.id)).where(Feedback.created_at >= week_start)
            ).scalar()
            
            return FeedbackStatsResponse(
                total_count=total_count or 0,
                pending_count=pending_count or 0,
                processing_count=processing_count or 0,
                resolved_count=resolved_count or 0,
                ignored_count=ignored_count or 0,
                today_count=today_count or 0,
                week_count=week_count or 0
            )
            
        except Exception as e:
            logger.error(f"获取反馈统计失败: {str(e)}")
            raise
    
    def get_user_feedbacks(
        self, 
        db: Session, 
        user_id: int, 
        page: int = 1, 
        page_size: int = 20
    ) -> Tuple[List[Feedback], int]:
        """
        获取用户的反馈列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            page: 页码
            page_size: 每页数量
            
        Returns:
            (反馈列表, 总数量)
        """
        query_params = FeedbackListQuery(
            user_id=user_id,
            page=page,
            page_size=page_size,
            order_by="created_at",
            order_desc=True
        )
        
        return self.get_feedback_list(db, query_params, include_user_info=False)
