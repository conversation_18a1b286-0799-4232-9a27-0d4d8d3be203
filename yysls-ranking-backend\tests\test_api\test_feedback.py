"""
反馈API接口测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models.feedback import Feedback, FeedbackStatus
from app.models.user import User, UserRole
from app.utils.security import create_access_token


class TestFeedbackAPI:
    """反馈API测试类"""
    
    @pytest.fixture
    def test_user(self, db: Session):
        """测试用户"""
        user = User(
            username="testuser",
            nickname="测试用户",
            password_hash="hashed_password",
            role=UserRole.USER.value,
            is_active=True,
            is_verified=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @pytest.fixture
    def admin_user(self, db: Session):
        """管理员用户"""
        user = User(
            username="admin",
            nickname="管理员",
            password_hash="hashed_password",
            role=UserRole.ADMIN.value,
            is_active=True,
            is_verified=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @pytest.fixture
    def user_token(self, test_user: User):
        """用户访问令牌"""
        return create_access_token(data={"sub": str(test_user.id)})
    
    @pytest.fixture
    def admin_token(self, admin_user: User):
        """管理员访问令牌"""
        return create_access_token(data={"sub": str(admin_user.id)})
    
    def test_create_feedback_authenticated(self, client: TestClient, user_token: str):
        """测试已登录用户提交反馈"""
        headers = {"Authorization": f"Bearer {user_token}"}
        data = {
            "content": "这是一个测试反馈",
            "user_id": None  # 会被当前用户ID覆盖
        }
        
        response = client.post("/api/v1/feedback/", json=data, headers=headers)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert result["message"] == "反馈提交成功"
        assert result["data"]["content"] == "这是一个测试反馈"
        assert result["data"]["status"] == "pending"
        assert result["data"]["user_id"] is not None
    
    def test_create_feedback_anonymous(self, client: TestClient):
        """测试匿名用户提交反馈"""
        data = {
            "content": "这是一个匿名反馈",
            "user_id": None
        }
        
        response = client.post("/api/v1/feedback/", json=data)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert result["message"] == "反馈提交成功"
        assert result["data"]["content"] == "这是一个匿名反馈"
        assert result["data"]["user_id"] is None
    
    def test_create_feedback_invalid_content(self, client: TestClient):
        """测试提交无效内容的反馈"""
        data = {
            "content": "",  # 空内容
            "user_id": None
        }
        
        response = client.post("/api/v1/feedback/", json=data)
        
        assert response.status_code == 422  # 验证错误
    
    def test_get_my_feedbacks(self, client: TestClient, db: Session, test_user: User, user_token: str):
        """测试获取我的反馈列表"""
        # 创建测试数据
        feedback = Feedback(
            content="我的测试反馈",
            user_id=test_user.id,
            status=FeedbackStatus.PENDING.value
        )
        db.add(feedback)
        db.commit()
        
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get("/api/v1/feedback/my", headers=headers)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert result["data"]["total"] == 1
        assert len(result["data"]["items"]) == 1
        assert result["data"]["items"][0]["content"] == "我的测试反馈"
    
    def test_get_my_feedbacks_unauthorized(self, client: TestClient):
        """测试未登录获取我的反馈列表"""
        response = client.get("/api/v1/feedback/my")
        
        assert response.status_code == 401  # 未授权
    
    def test_admin_get_feedback_list(self, client: TestClient, db: Session, test_user: User, admin_token: str):
        """测试管理员获取反馈列表"""
        # 创建测试数据
        feedback = Feedback(
            content="管理员查看的反馈",
            user_id=test_user.id,
            status=FeedbackStatus.PENDING.value
        )
        db.add(feedback)
        db.commit()
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/v1/feedback/admin/list", headers=headers)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert result["data"]["total"] >= 1
    
    def test_admin_get_feedback_list_with_filters(self, client: TestClient, db: Session, test_user: User, admin_token: str):
        """测试管理员按条件筛选反馈列表"""
        # 创建测试数据
        feedback1 = Feedback(content="待处理反馈", user_id=test_user.id, status=FeedbackStatus.PENDING.value)
        feedback2 = Feedback(content="已处理反馈", user_id=test_user.id, status=FeedbackStatus.RESOLVED.value)
        db.add_all([feedback1, feedback2])
        db.commit()
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 按状态筛选
        response = client.get("/api/v1/feedback/admin/list?status=pending", headers=headers)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        # 应该只返回待处理的反馈
        pending_items = [item for item in result["data"]["items"] if item["status"] == "pending"]
        assert len(pending_items) >= 1
    
    def test_admin_get_feedback_list_unauthorized(self, client: TestClient, user_token: str):
        """测试普通用户访问管理员接口"""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get("/api/v1/feedback/admin/list", headers=headers)
        
        assert response.status_code == 403  # 权限不足
    
    def test_admin_get_feedback_detail(self, client: TestClient, db: Session, test_user: User, admin_token: str):
        """测试管理员获取反馈详情"""
        # 创建测试数据
        feedback = Feedback(
            content="详情测试反馈",
            user_id=test_user.id,
            status=FeedbackStatus.PENDING.value
        )
        db.add(feedback)
        db.commit()
        db.refresh(feedback)
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get(f"/api/v1/feedback/admin/{feedback.id}", headers=headers)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert result["data"]["content"] == "详情测试反馈"
        assert result["data"]["id"] == feedback.id
    
    def test_admin_get_nonexistent_feedback_detail(self, client: TestClient, admin_token: str):
        """测试管理员获取不存在的反馈详情"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/v1/feedback/admin/99999", headers=headers)
        
        assert response.status_code == 404  # 未找到
    
    def test_admin_update_feedback(self, client: TestClient, db: Session, test_user: User, admin_token: str):
        """测试管理员更新反馈"""
        # 创建测试数据
        feedback = Feedback(
            content="待更新的反馈",
            user_id=test_user.id,
            status=FeedbackStatus.PENDING.value
        )
        db.add(feedback)
        db.commit()
        db.refresh(feedback)
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        update_data = {
            "status": "resolved",
            "admin_reply": "问题已解决"
        }
        
        response = client.put(f"/api/v1/feedback/admin/{feedback.id}", json=update_data, headers=headers)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert result["data"]["status"] == "resolved"
        assert result["data"]["admin_reply"] == "问题已解决"
    
    def test_admin_update_nonexistent_feedback(self, client: TestClient, admin_token: str):
        """测试管理员更新不存在的反馈"""
        headers = {"Authorization": f"Bearer {admin_token}"}
        update_data = {
            "status": "resolved",
            "admin_reply": "测试回复"
        }
        
        response = client.put("/api/v1/feedback/admin/99999", json=update_data, headers=headers)
        
        assert response.status_code == 404  # 未找到
    
    def test_admin_get_feedback_stats(self, client: TestClient, db: Session, test_user: User, admin_token: str):
        """测试管理员获取反馈统计"""
        # 创建测试数据
        feedbacks_data = [
            ("反馈1", FeedbackStatus.PENDING),
            ("反馈2", FeedbackStatus.PROCESSING),
            ("反馈3", FeedbackStatus.RESOLVED),
        ]
        
        for content, status in feedbacks_data:
            feedback = Feedback(
                content=content,
                user_id=test_user.id,
                status=status.value
            )
            db.add(feedback)
        db.commit()
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/v1/feedback/admin/stats", headers=headers)
        
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert "total_count" in result["data"]
        assert "pending_count" in result["data"]
        assert "processing_count" in result["data"]
        assert "resolved_count" in result["data"]
        assert result["data"]["total_count"] >= 3
