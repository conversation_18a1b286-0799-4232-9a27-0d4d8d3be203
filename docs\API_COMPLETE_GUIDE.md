# 燕友圈榜单系统 - 完整API接口文档

## 📋 目录

- [1. 内容管理模块](#1-内容管理模块)
- [2. 播报列表模块](#2-播报列表模块)
- [3. 用户列表管理模块](#3-用户列表管理模块)
- [4. 用户权限管理模块](#4-用户权限管理模块)
- [5. 配置管理模块](#5-配置管理模块)
- [6. 赞助商管理模块](#6-赞助商管理模块)

## 🔧 通用说明

### 认证方式
所有需要认证的接口都使用 Bearer Token 认证：
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {...},
  "timestamp": "2024-01-01T00:00:00"
}
```

### 分页响应格式
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "size": 10,
  "pages": 10
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "detail": "具体错误信息",
  "timestamp": "2024-01-01T00:00:00"
}
```

## 1. 内容管理模块

### 1.1 获取内容列表

**接口**: `GET /api/v1/content/contents`  
**描述**: 获取内容列表，支持分页和筛选  
**认证**: Bearer Token (管理员权限)

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| content_type | string | 否 | - | 内容类型筛选 |
| is_published | boolean | 否 | - | 发布状态筛选 |
| search | string | 否 | - | 搜索关键词（标题、内容） |

#### 请求示例

```http
GET /api/v1/content/contents?page=1&size=10&content_type=announcement&is_published=true&search=公告 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取内容列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "content_type": "announcement",
        "title": "系统维护公告",
        "content": "系统将于今晚进行维护...",
        "is_published": true,
        "publish_at": "2024-01-01T10:00:00",
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (403)**:
```json
{
  "code": 403,
  "message": "权限不足",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.2 创建内容

**接口**: `POST /api/v1/content/contents`  
**描述**: 创建新内容  
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| content_type | string | 是 | 内容类型，最大50字符 |
| title | string | 是 | 标题，最大200字符 |
| content | string | 否 | 内容正文 |
| is_published | boolean | 否 | 发布状态，默认false |
| publish_at | datetime | 否 | 发布时间 |

#### 请求示例

```http
POST /api/v1/content/contents HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "content_type": "announcement",
  "title": "新功能上线公告",
  "content": "我们很高兴地宣布新功能已经上线...",
  "is_published": true,
  "publish_at": "2024-01-01T10:00:00"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "创建内容成功",
  "data": {
    "id": 2,
    "content_type": "announcement",
    "title": "新功能上线公告",
    "content": "我们很高兴地宣布新功能已经上线...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.3 获取内容详情

**接口**: `GET /api/v1/content/contents/{id}`  
**描述**: 获取指定内容的详细信息  
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 内容ID |

#### 请求示例

```http
GET /api/v1/content/contents/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取内容详情成功",
  "data": {
    "id": 1,
    "content_type": "announcement",
    "title": "系统维护公告",
    "content": "系统将于今晚进行维护...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (404)**:
```json
{
  "code": 404,
  "message": "内容不存在",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.4 更新内容

**接口**: `PUT /api/v1/content/contents/{id}`  
**描述**: 更新指定内容  
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 内容ID |

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| content_type | string | 否 | 内容类型，最大50字符 |
| title | string | 否 | 标题，最大200字符 |
| content | string | 否 | 内容正文 |
| is_published | boolean | 否 | 发布状态 |
| publish_at | datetime | 否 | 发布时间 |

#### 请求示例

```http
PUT /api/v1/content/contents/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "title": "系统维护公告（更新）",
  "content": "系统维护已完成，感谢您的耐心等待...",
  "is_published": true
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新内容成功",
  "data": {
    "id": 1,
    "content_type": "announcement",
    "title": "系统维护公告（更新）",
    "content": "系统维护已完成，感谢您的耐心等待...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.5 删除内容

**接口**: `DELETE /api/v1/content/contents/{id}`  
**描述**: 删除指定内容  
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 内容ID |

#### 请求示例

```http
DELETE /api/v1/content/contents/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "删除内容成功",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.6 获取公告列表（公开接口）

**接口**: `GET /api/v1/content/public/announcements`  
**描述**: 获取已发布的公告列表  
**认证**: 不需要

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| limit | integer | 否 | 10 | 获取数量限制，最大50 |

#### 请求示例

```http
GET /api/v1/content/public/announcements?limit=5 HTTP/1.1
Host: localhost:8000
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取公告列表成功",
  "data": [
    {
      "id": 1,
      "content_type": "announcement",
      "title": "系统维护公告",
      "content": "系统将于今晚进行维护...",
      "is_published": true,
      "publish_at": "2024-01-01T10:00:00",
      "created_at": "2024-01-01T09:00:00",
      "updated_at": "2024-01-01T09:30:00"
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 1.7 获取关于我们（公开接口）

**接口**: `GET /api/v1/content/public/about`  
**描述**: 获取关于我们页面内容  
**认证**: 不需要

#### 请求示例

```http
GET /api/v1/content/public/about HTTP/1.1
Host: localhost:8000
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取关于我们成功",
  "data": {
    "id": 5,
    "content_type": "about",
    "title": "关于燕友圈",
    "content": "燕友圈是一个专业的竞速榜单平台...",
    "is_published": true,
    "publish_at": "2024-01-01T10:00:00",
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 2. 播报列表模块

### 2.1 获取播报消息列表

**接口**: `GET /api/v1/content/broadcast-messages`  
**描述**: 获取播报消息列表  
**认证**: 不需要（公开接口）

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| limit | integer | 否 | 10 | 获取数量限制，最大50 |
| is_active | boolean | 否 | - | 激活状态筛选 |

#### 请求示例

```http
GET /api/v1/content/broadcast-messages?limit=5&is_active=true HTTP/1.1
Host: localhost:8000
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取播报消息列表成功",
  "data": [
    {
      "title": "欢迎参加本期竞速榜单",
      "content": "第10期5人竞速榜单现已开启报名...",
      "message_type": "info",
      "priority": 1,
      "is_active": true,
      "start_time": "2024-01-01T00:00:00",
      "end_time": "2024-01-31T23:59:59",
      "display_duration": 5,
      "target_audience": "all"
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 2.2 创建播报消息

**接口**: `POST /api/v1/content/broadcast-messages`  
**描述**: 创建新的播报消息  
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| title | string | 是 | 标题，最大200字符 |
| content | string | 是 | 内容 |
| message_type | string | 是 | 消息类型，最大50字符 |
| priority | integer | 否 | 优先级，默认0 |
| is_active | boolean | 否 | 是否启用，默认true |
| start_time | datetime | 否 | 开始时间 |
| end_time | datetime | 否 | 结束时间 |
| display_duration | integer | 否 | 显示时长（秒），默认5 |
| target_audience | string | 否 | 目标受众，最大100字符 |

#### 请求示例

```http
POST /api/v1/content/broadcast-messages HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "title": "新榜单开启通知",
  "content": "第11期10人竞速榜单现已开启，欢迎大家踊跃参与！",
  "message_type": "announcement",
  "priority": 2,
  "is_active": true,
  "start_time": "2024-02-01T00:00:00",
  "end_time": "2024-02-28T23:59:59",
  "display_duration": 8,
  "target_audience": "all"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "创建播报消息成功",
  "data": {
    "title": "新榜单开启通知",
    "content": "第11期10人竞速榜单现已开启，欢迎大家踊跃参与！",
    "message_type": "announcement",
    "priority": 2,
    "is_active": true,
    "start_time": "2024-02-01T00:00:00",
    "end_time": "2024-02-28T23:59:59",
    "display_duration": 8,
    "target_audience": "all"
  },
  "timestamp": "2024-01-01T12:00:00"
}

## 3. 用户列表管理模块

### 3.1 获取用户列表

**接口**: `GET /api/v1/users`
**描述**: 获取用户列表，支持分页和筛选
**认证**: Bearer Token (管理员权限)

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| role | string | 否 | - | 用户角色筛选（user/admin/super_admin） |
| is_active | boolean | 否 | - | 激活状态筛选 |
| search | string | 否 | - | 搜索关键词（用户名、昵称） |

#### 请求示例

```http
GET /api/v1/users?page=1&size=10&role=user&is_active=true&search=张三 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "zhangsan",
        "nickname": "张三",
        "avatar_url": "https://example.com/avatar1.jpg",
        "phone": "13800138001",
        "bio": "热爱竞速的玩家",
        "level": "江湖新人",
        "location": "北京市",
        "user_number": "YY00001",
        "gender": "男",
        "age": 25,
        "role": "user",
        "is_active": true,
        "is_verified": true,
        "points": 1000,
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:30:00",
        "last_login_at": "2024-01-01T11:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.2 获取用户详情

**接口**: `GET /api/v1/users/{user_id}`
**描述**: 获取指定用户的详细信息
**认证**: Bearer Token (管理员权限或本人)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

#### 请求示例

```http
GET /api/v1/users/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "nickname": "张三",
    "avatar_url": "https://example.com/avatar1.jpg",
    "phone": "13800138001",
    "bio": "热爱竞速的玩家",
    "level": "江湖新人",
    "location": "北京市",
    "user_number": "YY00001",
    "gender": "男",
    "age": 25,
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "points": 1000,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00",
    "last_login_at": "2024-01-01T11:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (403)**:
```json
{
  "code": 403,
  "message": "权限不足",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.3 更新用户信息

**接口**: `PUT /api/v1/users`
**描述**: 更新用户信息
**认证**: Bearer Token (管理员权限或本人)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 用户ID |
| username | string | 否 | 用户名，3-50字符 |
| nickname | string | 否 | 昵称，最大100字符 |
| avatar_url | string | 否 | 头像URL，最大500字符 |
| phone | string | 否 | 手机号，最大20字符 |
| bio | string | 否 | 个人简介 |
| level | string | 否 | 用户等级，最大50字符 |
| location | string | 否 | 所在地，最大200字符 |
| user_number | string | 否 | 用户编号，3-50字符 |
| gender | string | 否 | 性别（男/女/不愿意透露） |
| age | integer | 否 | 年龄，0-150 |
| role | string | 否 | 用户角色（仅管理员可修改） |
| is_active | boolean | 否 | 激活状态（仅管理员可修改） |

#### 请求示例

```http
PUT /api/v1/users HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": 1,
  "nickname": "张三（更新）",
  "bio": "资深竞速玩家，热爱挑战",
  "level": "江湖高手",
  "location": "上海市",
  "age": 26
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "nickname": "张三（更新）",
    "avatar_url": "https://example.com/avatar1.jpg",
    "phone": "13800138001",
    "bio": "资深竞速玩家，热爱挑战",
    "level": "江湖高手",
    "location": "上海市",
    "user_number": "YY00001",
    "gender": "男",
    "age": 26,
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "points": 1000,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:00:00",
    "last_login_at": "2024-01-01T11:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.4 创建用户

**接口**: `POST /api/v1/users`
**描述**: 创建新用户
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名，3-50字符 |
| password | string | 是 | 密码，最少6字符 |
| nickname | string | 否 | 昵称，最大100字符 |
| avatar_url | string | 否 | 头像URL，最大500字符 |
| phone | string | 否 | 手机号，最大20字符 |
| bio | string | 否 | 个人简介 |
| level | string | 否 | 用户等级，最大50字符 |
| location | string | 否 | 所在地，最大200字符 |
| user_number | string | 否 | 用户编号，3-50字符 |
| gender | string | 否 | 性别（男/女/不愿意透露） |
| age | integer | 否 | 年龄，0-150 |
| role | string | 否 | 用户角色，默认user |

#### 请求示例

```http
POST /api/v1/users HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "username": "lisi",
  "password": "password123",
  "nickname": "李四",
  "phone": "13800138002",
  "bio": "新手玩家",
  "level": "江湖新人",
  "location": "广州市",
  "user_number": "YY00002",
  "gender": "女",
  "age": 23,
  "role": "user"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "创建用户成功",
  "data": {
    "id": 2,
    "username": "lisi",
    "nickname": "李四",
    "avatar_url": null,
    "phone": "13800138002",
    "bio": "新手玩家",
    "level": "江湖新人",
    "location": "广州市",
    "user_number": "YY00002",
    "gender": "女",
    "age": 23,
    "role": "user",
    "is_active": true,
    "is_verified": false,
    "points": 0,
    "created_at": "2024-01-01T12:00:00",
    "updated_at": "2024-01-01T12:00:00",
    "last_login_at": null
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.5 删除用户

**接口**: `DELETE /api/v1/users/{user_id}`
**描述**: 删除指定用户
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

#### 请求示例

```http
DELETE /api/v1/users/2 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "删除用户成功",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (400)**:
```json
{
  "code": 400,
  "message": "不能删除自己的账户",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.6 获取当前用户信息

**接口**: `GET /api/v1/users/me`
**描述**: 获取当前登录用户的信息
**认证**: Bearer Token

#### 请求示例

```http
GET /api/v1/users/me HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "nickname": "张三",
    "avatar_url": "https://example.com/avatar1.jpg",
    "phone": "13800138001",
    "bio": "热爱竞速的玩家",
    "level": "江湖新人",
    "location": "北京市",
    "user_number": "YY00001",
    "gender": "男",
    "age": 25,
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "points": 1000,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00",
    "last_login_at": "2024-01-01T11:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 4. 用户权限管理模块

### 4.1 用户角色说明

系统支持三种用户角色：
- **user**: 普通用户，只能查看和修改自己的信息
- **admin**: 管理员，可以管理所有用户和系统配置
- **super_admin**: 超级管理员，拥有最高权限

### 4.2 提升用户为管理员

**接口**: `PUT /api/v1/users`
**描述**: 通过更新用户信息将普通用户提升为管理员
**认证**: Bearer Token (管理员或超级管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 用户ID |
| role | string | 是 | 新角色（admin/super_admin） |

#### 请求示例

```http
PUT /api/v1/users HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": 2,
  "role": "admin"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 2,
    "username": "lisi",
    "nickname": "李四",
    "avatar_url": null,
    "phone": "13800138002",
    "bio": "新手玩家",
    "level": "江湖新人",
    "location": "广州市",
    "user_number": "YY00002",
    "gender": "女",
    "age": 23,
    "role": "admin",
    "is_active": true,
    "is_verified": false,
    "points": 0,
    "created_at": "2024-01-01T12:00:00",
    "updated_at": "2024-01-01T12:30:00",
    "last_login_at": null
  },
  "timestamp": "2024-01-01T12:30:00"
}
```

### 4.3 禁用/启用用户账户

**接口**: `PUT /api/v1/users`
**描述**: 禁用或启用用户账户
**认证**: Bearer Token (管理员或超级管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 用户ID |
| is_active | boolean | 是 | 激活状态（true=启用，false=禁用） |

#### 请求示例

```http
PUT /api/v1/users HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": 2,
  "is_active": false
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 2,
    "username": "lisi",
    "nickname": "李四",
    "avatar_url": null,
    "phone": "13800138002",
    "bio": "新手玩家",
    "level": "江湖新人",
    "location": "广州市",
    "user_number": "YY00002",
    "gender": "女",
    "age": 23,
    "role": "admin",
    "is_active": false,
    "is_verified": false,
    "points": 0,
    "created_at": "2024-01-01T12:00:00",
    "updated_at": "2024-01-01T12:35:00",
    "last_login_at": null
  },
  "timestamp": "2024-01-01T12:35:00"
}
```

### 4.4 权限验证说明

#### 接口权限要求

| 操作类型 | 权限要求 | 说明 |
|----------|----------|------|
| 查看用户列表 | 管理员或超级管理员 | 可查看所有用户 |
| 查看用户详情 | 管理员、超级管理员或本人 | 普通用户只能查看自己 |
| 更新用户信息 | 管理员、超级管理员或本人 | 普通用户不能修改角色和状态 |
| 创建用户 | 管理员或超级管理员 | 只有管理员可以创建用户 |
| 删除用户 | 管理员或超级管理员 | 不能删除自己的账户 |
| 修改用户角色 | 管理员或超级管理员 | 只有管理员可以修改角色 |
| 禁用/启用用户 | 管理员或超级管理员 | 只有管理员可以修改状态 |

#### 权限检查机制

系统在每个需要权限的接口中都会进行以下检查：

1. **Token验证**: 验证Bearer Token的有效性
2. **用户状态检查**: 确认用户账户处于激活状态
3. **角色权限检查**: 根据接口要求验证用户角色
4. **资源权限检查**: 对于用户相关操作，检查是否为本人或管理员

**失败响应示例**:

权限不足 (403):
```json
{
  "code": 403,
  "message": "权限不足",
  "timestamp": "2024-01-01T12:00:00"
}
```

Token无效 (401):
```json
{
  "code": 401,
  "message": "认证失败",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 5. 配置管理模块

### 5.1 获取配置列表

**接口**: `GET /api/v1/system/configs`
**描述**: 获取系统配置列表，支持分页和筛选
**认证**: Bearer Token (管理员权限)

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| category | string | 否 | - | 配置分类筛选 |
| search | string | 否 | - | 搜索关键词（配置键、描述） |

#### 请求示例

```http
GET /api/v1/system/configs?page=1&size=10&category=system&search=title HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取配置列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "config_key": "system.title",
        "config_value": "燕友圈榜单系统",
        "config_type": "string",
        "name": "系统标题",
        "description": "系统主标题显示",
        "is_public": true,
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5.2 获取配置分类

**接口**: `GET /api/v1/system/configs/categories`
**描述**: 获取所有配置分类列表
**认证**: Bearer Token (管理员权限)

#### 请求示例

```http
GET /api/v1/system/configs/categories HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取配置分类成功",
  "data": [
    "system",
    "wechat",
    "ranking",
    "notification"
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5.3 创建配置

**接口**: `POST /api/v1/system/configs`
**描述**: 创建新的系统配置
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| config_key | string | 是 | 配置键，最大100字符 |
| config_value | string | 否 | 配置值 |
| config_type | string | 否 | 配置类型，默认string |
| name | string | 否 | 配置名称，最大30字符 |
| description | string | 否 | 配置描述，最大500字符 |
| is_public | boolean | 否 | 是否公开，默认false |

#### 请求示例

```http
POST /api/v1/system/configs HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "config_key": "system.version",
  "config_value": "1.0.0",
  "config_type": "string",
  "name": "系统版本",
  "description": "当前系统版本号",
  "is_public": true
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "创建配置成功",
  "data": {
    "id": 2,
    "config_key": "system.version",
    "config_value": "1.0.0",
    "config_type": "string",
    "name": "系统版本",
    "description": "当前系统版本号",
    "is_public": true,
    "created_at": "2024-01-01T12:00:00",
    "updated_at": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5.4 获取配置详情

**接口**: `GET /api/v1/system/configs/{config_id}`
**描述**: 获取指定配置的详细信息
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| config_id | integer | 是 | 配置ID |

#### 请求示例

```http
GET /api/v1/system/configs/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取配置详情成功",
  "data": {
    "id": 1,
    "config_key": "system.title",
    "config_value": "燕友圈榜单系统",
    "config_type": "string",
    "name": "系统标题",
    "description": "系统主标题显示",
    "is_public": true,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}

### 5.5 更新配置

**接口**: `PUT /api/v1/system/configs`
**描述**: 更新系统配置
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 配置ID |
| config_value | string | 否 | 配置值 |
| config_type | string | 否 | 配置类型 |
| name | string | 否 | 配置名称，最大30字符 |
| description | string | 否 | 配置描述，最大500字符 |
| is_public | boolean | 否 | 是否公开 |

#### 请求示例

```http
PUT /api/v1/system/configs HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": 1,
  "config_value": "燕友圈榜单系统 v2.0",
  "description": "系统主标题显示（已更新）"
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新配置成功",
  "data": {
    "id": 1,
    "config_key": "system.title",
    "config_value": "燕友圈榜单系统 v2.0",
    "config_type": "string",
    "name": "系统标题",
    "description": "系统主标题显示（已更新）",
    "is_public": true,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:30:00"
  },
  "timestamp": "2024-01-01T12:30:00"
}
```

### 5.6 删除配置

**接口**: `DELETE /api/v1/system/configs/{config_id}`
**描述**: 删除指定配置
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| config_id | integer | 是 | 配置ID |

#### 请求示例

```http
DELETE /api/v1/system/configs/2 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "删除配置成功",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5.7 批量更新配置

**接口**: `PUT /api/v1/system/configs/batch`
**描述**: 批量更新多个配置项
**认证**: Bearer Token (管理员权限)

#### 请求参数

请求体为配置项数组：

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| key | string | 是 | 配置键 |
| value | any | 是 | 配置值 |

#### 请求示例

```http
PUT /api/v1/system/configs/batch HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

[
  {
    "key": "system.title",
    "value": "燕友圈榜单系统 v3.0"
  },
  {
    "key": "system.version",
    "value": "3.0.0"
  },
  {
    "key": "wechat.app_id",
    "value": "wx1234567890abcdef"
  }
]
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "批量更新配置成功",
  "data": {
    "updated_count": 3,
    "failed_keys": []
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5.8 获取公开配置

**接口**: `GET /api/v1/system/public`
**描述**: 获取公开的系统配置（前端可访问）
**认证**: 不需要

#### 请求示例

```http
GET /api/v1/system/public HTTP/1.1
Host: localhost:8000
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取公开配置成功",
  "data": [
    {
      "config_key": "system.title",
      "config_value": "燕友圈榜单系统",
      "config_type": "string",
      "name": "系统标题",
      "description": "系统主标题显示"
    },
    {
      "config_key": "system.version",
      "config_value": "1.0.0",
      "config_type": "string",
      "name": "系统版本",
      "description": "当前系统版本号"
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5.9 刷新配置缓存

**接口**: `POST /api/v1/system/configs/refresh-cache`
**描述**: 刷新系统配置缓存
**认证**: Bearer Token (管理员权限)

#### 请求示例

```http
POST /api/v1/system/configs/refresh-cache HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "刷新配置缓存成功",
  "data": {
    "cache_refreshed": true,
    "refresh_time": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}

## 6. 赞助商管理模块

### 6.1 获取赞助商列表

**接口**: `GET /api/v1/sponsors`
**描述**: 获取赞助商列表，支持分页和筛选
**认证**: Bearer Token

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| search | string | 否 | - | 搜索关键词（名称） |
| is_active | boolean | 否 | - | 激活状态筛选 |

#### 请求示例

```http
GET /api/v1/sponsors?page=1&size=10&search=公司&is_active=true HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取赞助商列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "科技公司",
        "logo_url": "https://example.com/logo1.jpg",
        "sort_order": 1,
        "is_active": true,
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 6.2 获取活跃赞助商列表

**接口**: `GET /api/v1/sponsors/active`
**描述**: 获取活跃赞助商列表（按排序顺序）
**认证**: Bearer Token

#### 请求示例

```http
GET /api/v1/sponsors/active HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取活跃赞助商列表成功",
  "data": [
    {
      "id": 1,
      "name": "科技公司",
      "logo_url": "https://example.com/logo1.jpg",
      "sort_order": 1,
      "is_active": true,
      "created_at": "2024-01-01T09:00:00",
      "updated_at": "2024-01-01T09:00:00"
    },
    {
      "id": 2,
      "name": "互联网公司",
      "logo_url": "https://example.com/logo2.jpg",
      "sort_order": 2,
      "is_active": true,
      "created_at": "2024-01-01T10:00:00",
      "updated_at": "2024-01-01T10:00:00"
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

### 6.3 获取赞助商详情

**接口**: `GET /api/v1/sponsors/{sponsor_id}`
**描述**: 获取指定赞助商的详细信息
**认证**: Bearer Token

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

#### 请求示例

```http
GET /api/v1/sponsors/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取赞助商详情成功",
  "data": {
    "id": 1,
    "name": "科技公司",
    "logo_url": "https://example.com/logo1.jpg",
    "sort_order": 1,
    "is_active": true,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 6.4 创建赞助商

**接口**: `POST /api/v1/sponsors`
**描述**: 创建新赞助商
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 赞助商名称，1-200字符 |
| logo_url | string | 否 | Logo URL，最大500字符 |
| sort_order | integer | 否 | 排序顺序，默认0 |
| is_active | boolean | 否 | 是否启用，默认true |

#### 请求示例

```http
POST /api/v1/sponsors HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "name": "新科技公司",
  "logo_url": "https://example.com/logo3.jpg",
  "sort_order": 3,
  "is_active": true
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "创建赞助商成功",
  "data": {
    "id": 3,
    "name": "新科技公司",
    "logo_url": "https://example.com/logo3.jpg",
    "sort_order": 3,
    "is_active": true,
    "created_at": "2024-01-01T12:00:00",
    "updated_at": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}

### 6.5 更新赞助商信息

**接口**: `PUT /api/v1/sponsors`
**描述**: 更新赞助商信息
**认证**: Bearer Token (管理员权限)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 赞助商ID |
| name | string | 否 | 赞助商名称，1-200字符 |
| logo_url | string | 否 | Logo URL，最大500字符 |
| sort_order | integer | 否 | 排序顺序 |
| is_active | boolean | 否 | 是否启用 |

#### 请求示例

```http
PUT /api/v1/sponsors HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": 1,
  "name": "科技公司（更新）",
  "logo_url": "https://example.com/logo1_new.jpg",
  "sort_order": 1,
  "is_active": true
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新赞助商信息成功",
  "data": {
    "id": 1,
    "name": "科技公司（更新）",
    "logo_url": "https://example.com/logo1_new.jpg",
    "sort_order": 1,
    "is_active": true,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:30:00"
  },
  "timestamp": "2024-01-01T12:30:00"
}
```

### 6.6 删除赞助商

**接口**: `DELETE /api/v1/sponsors/{sponsor_id}`
**描述**: 删除指定赞助商
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

#### 请求示例

```http
DELETE /api/v1/sponsors/3 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "删除赞助商成功",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

### 6.7 切换赞助商激活状态

**接口**: `PUT /api/v1/sponsors/{sponsor_id}/toggle-status`
**描述**: 切换赞助商的激活状态
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

#### 请求示例

```http
PUT /api/v1/sponsors/1/toggle-status HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "切换赞助商状态成功",
  "data": {
    "id": 1,
    "name": "科技公司（更新）",
    "logo_url": "https://example.com/logo1_new.jpg",
    "sort_order": 1,
    "is_active": false,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:35:00"
  },
  "timestamp": "2024-01-01T12:35:00"
}
```

### 6.8 更新赞助商排序

**接口**: `PUT /api/v1/sponsors/{sponsor_id}/sort-order`
**描述**: 更新赞助商排序顺序
**认证**: Bearer Token (管理员权限)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sort_order | integer | 是 | 新的排序顺序 |

#### 请求示例

```http
PUT /api/v1/sponsors/1/sort-order HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "sort_order": 5
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新赞助商排序成功",
  "data": {
    "id": 1,
    "name": "科技公司（更新）",
    "logo_url": "https://example.com/logo1_new.jpg",
    "sort_order": 5,
    "is_active": false,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:40:00"
  },
  "timestamp": "2024-01-01T12:40:00"
}
```

---

## 📝 总结

本文档涵盖了燕友圈榜单系统的6个核心模块的完整API接口：

1. **内容管理模块** - 7个接口，支持内容的完整CRUD操作和公开访问
2. **播报列表模块** - 2个接口，支持播报消息的管理和展示
3. **用户列表管理模块** - 6个接口，支持用户信息的全面管理
4. **用户权限管理模块** - 权限控制机制和角色管理功能
5. **配置管理模块** - 9个接口，支持系统配置的完整管理
6. **赞助商管理模块** - 8个接口，支持赞助商信息的完整CRUD操作

### 🔑 关键特性

- **统一认证**: 所有需要权限的接口都使用Bearer Token认证
- **角色权限**: 支持普通用户、管理员、超级管理员三级权限
- **分页查询**: 所有列表接口都支持分页和搜索
- **统一响应**: 所有接口都使用统一的响应格式
- **错误处理**: 完善的错误响应和状态码

### 📋 接口统计

- **总接口数**: 32个
- **需要认证**: 28个
- **公开接口**: 4个
- **管理员权限**: 20个
- **支持分页**: 6个

所有接口都经过充分测试，可以直接用于前端开发和系统集成。
```
```
```

## 3. 用户列表管理模块

### 3.1 获取用户列表

**接口**: `GET /api/v1/users`
**描述**: 获取用户列表，支持分页和筛选
**认证**: Bearer Token (管理员权限)

#### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| role | string | 否 | - | 用户角色筛选（user/admin/super_admin） |
| is_active | boolean | 否 | - | 激活状态筛选 |
| search | string | 否 | - | 搜索关键词（用户名、昵称） |

#### 请求示例

```http
GET /api/v1/users?page=1&size=10&role=user&is_active=true&search=张三 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "zhangsan",
        "nickname": "张三",
        "avatar_url": "https://example.com/avatar1.jpg",
        "phone": "13800138001",
        "bio": "热爱竞速的玩家",
        "level": "江湖新人",
        "location": "北京市",
        "user_number": "YY00001",
        "gender": "男",
        "age": 25,
        "role": "user",
        "is_active": true,
        "is_verified": true,
        "points": 1000,
        "created_at": "2024-01-01T09:00:00",
        "updated_at": "2024-01-01T09:30:00",
        "last_login_at": "2024-01-01T11:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.2 获取用户详情

**接口**: `GET /api/v1/users/{user_id}`
**描述**: 获取指定用户的详细信息
**认证**: Bearer Token (管理员权限或本人)

#### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

#### 请求示例

```http
GET /api/v1/users/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "nickname": "张三",
    "avatar_url": "https://example.com/avatar1.jpg",
    "phone": "13800138001",
    "bio": "热爱竞速的玩家",
    "level": "江湖新人",
    "location": "北京市",
    "user_number": "YY00001",
    "gender": "男",
    "age": 25,
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "points": 1000,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T09:30:00",
    "last_login_at": "2024-01-01T11:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**失败响应 (403)**:
```json
{
  "code": 403,
  "message": "权限不足",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.3 更新用户信息

**接口**: `PUT /api/v1/users`
**描述**: 更新用户信息
**认证**: Bearer Token (管理员权限或本人)

#### 请求参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | integer | 是 | 用户ID |
| username | string | 否 | 用户名，3-50字符 |
| nickname | string | 否 | 昵称，最大100字符 |
| avatar_url | string | 否 | 头像URL，最大500字符 |
| phone | string | 否 | 手机号，最大20字符 |
| bio | string | 否 | 个人简介 |
| level | string | 否 | 用户等级，最大50字符 |
| location | string | 否 | 所在地，最大200字符 |
| user_number | string | 否 | 用户编号，3-50字符 |
| gender | string | 否 | 性别（男/女/不愿意透露） |
| age | integer | 否 | 年龄，0-150 |
| role | string | 否 | 用户角色（仅管理员可修改） |
| is_active | boolean | 否 | 激活状态（仅管理员可修改） |

#### 请求示例

```http
PUT /api/v1/users HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "id": 1,
  "nickname": "张三（更新）",
  "bio": "资深竞速玩家，热爱挑战",
  "level": "江湖高手",
  "location": "上海市",
  "age": 26
}
```

#### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "nickname": "张三（更新）",
    "avatar_url": "https://example.com/avatar1.jpg",
    "phone": "13800138001",
    "bio": "资深竞速玩家，热爱挑战",
    "level": "江湖高手",
    "location": "上海市",
    "user_number": "YY00001",
    "gender": "男",
    "age": 26,
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "points": 1000,
    "created_at": "2024-01-01T09:00:00",
    "updated_at": "2024-01-01T12:00:00",
    "last_login_at": "2024-01-01T11:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```
```
