"""create_feedback_table_only

Revision ID: c491c7e3304c
Revises: 12a0df74f3ea
Create Date: 2025-08-04 15:03:34.928164

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c491c7e3304c'
down_revision = '12a0df74f3ea'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table('feedback',
    sa.<PERSON>umn('id', sa.Integer(), autoincrement=True, nullable=False, comment='反馈ID'),
    sa.Column('content', sa.Text(), nullable=False, comment='反馈内容'),
    sa.Column('user_id', sa.Integer(), nullable=True, comment='用户ID，可为空支持匿名反馈'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='反馈状态'),
    sa.<PERSON>umn('admin_reply', sa.Text(), nullable=True, comment='管理员回复'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='用户反馈表',
    mysql_charset='utf8mb4',
    mysql_collate='utf8mb4_unicode_ci',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_feedback_status'), 'feedback', ['status'], unique=False)
    op.create_index(op.f('ix_feedback_user_id'), 'feedback', ['user_id'], unique=False)


def downgrade() -> None:
    op.drop_index(op.f('ix_feedback_user_id'), table_name='feedback')
    op.drop_index(op.f('ix_feedback_status'), table_name='feedback')
    op.drop_table('feedback')
