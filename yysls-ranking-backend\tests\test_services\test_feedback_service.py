"""
反馈服务层测试
"""
import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.services.feedback_service import FeedbackService
from app.models.feedback import Feedback, FeedbackStatus
from app.models.user import User, UserRole
from app.schemas.feedback import FeedbackCreate, FeedbackUpdate, FeedbackListQuery


class TestFeedbackService:
    """反馈服务测试类"""
    
    @pytest.fixture
    def feedback_service(self):
        """反馈服务实例"""
        return FeedbackService()
    
    @pytest.fixture
    def test_user(self, db: Session):
        """测试用户"""
        user = User(
            username="testuser",
            nickname="测试用户",
            password_hash="hashed_password",
            role=UserRole.USER.value,
            is_active=True,
            is_verified=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @pytest.fixture
    def admin_user(self, db: Session):
        """管理员用户"""
        user = User(
            username="admin",
            nickname="管理员",
            password_hash="hashed_password",
            role=UserRole.ADMIN.value,
            is_active=True,
            is_verified=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    def test_create_feedback_with_user(self, db: Session, feedback_service: FeedbackService, test_user: User):
        """测试创建用户反馈"""
        feedback_data = FeedbackCreate(
            content="这是一个测试反馈",
            user_id=test_user.id
        )
        
        feedback = feedback_service.create_feedback(
            db=db,
            feedback_data=feedback_data,
            current_user_id=test_user.id
        )
        
        assert feedback is not None
        assert feedback.content == "这是一个测试反馈"
        assert feedback.user_id == test_user.id
        assert feedback.status == FeedbackStatus.PENDING.value
        assert feedback.admin_reply is None
        assert feedback.created_at is not None
        assert feedback.updated_at is not None
    
    def test_create_anonymous_feedback(self, db: Session, feedback_service: FeedbackService):
        """测试创建匿名反馈"""
        feedback_data = FeedbackCreate(
            content="这是一个匿名反馈",
            user_id=None
        )
        
        feedback = feedback_service.create_feedback(
            db=db,
            feedback_data=feedback_data,
            current_user_id=None
        )
        
        assert feedback is not None
        assert feedback.content == "这是一个匿名反馈"
        assert feedback.user_id is None
        assert feedback.status == FeedbackStatus.PENDING.value
    
    def test_get_feedback_list(self, db: Session, feedback_service: FeedbackService, test_user: User):
        """测试获取反馈列表"""
        # 创建测试数据
        feedbacks_data = [
            ("反馈1", FeedbackStatus.PENDING),
            ("反馈2", FeedbackStatus.PROCESSING),
            ("反馈3", FeedbackStatus.RESOLVED),
        ]
        
        for content, status in feedbacks_data:
            feedback = Feedback(
                content=content,
                user_id=test_user.id,
                status=status.value
            )
            db.add(feedback)
        db.commit()
        
        # 测试获取所有反馈
        query_params = FeedbackListQuery(page=1, page_size=10)
        feedbacks, total = feedback_service.get_feedback_list(db, query_params)
        
        assert total == 3
        assert len(feedbacks) == 3
    
    def test_get_feedback_list_with_status_filter(self, db: Session, feedback_service: FeedbackService, test_user: User):
        """测试按状态筛选反馈列表"""
        # 创建测试数据
        feedback1 = Feedback(content="待处理反馈", user_id=test_user.id, status=FeedbackStatus.PENDING.value)
        feedback2 = Feedback(content="已处理反馈", user_id=test_user.id, status=FeedbackStatus.RESOLVED.value)
        db.add_all([feedback1, feedback2])
        db.commit()
        
        # 测试筛选待处理反馈
        query_params = FeedbackListQuery(status=FeedbackStatus.PENDING, page=1, page_size=10)
        feedbacks, total = feedback_service.get_feedback_list(db, query_params)
        
        assert total == 1
        assert len(feedbacks) == 1
        assert feedbacks[0].status == FeedbackStatus.PENDING.value
    
    def test_update_feedback(self, db: Session, feedback_service: FeedbackService, test_user: User):
        """测试更新反馈"""
        # 创建测试反馈
        feedback = Feedback(
            content="测试反馈",
            user_id=test_user.id,
            status=FeedbackStatus.PENDING.value
        )
        db.add(feedback)
        db.commit()
        db.refresh(feedback)
        
        # 更新反馈
        update_data = FeedbackUpdate(
            status=FeedbackStatus.RESOLVED,
            admin_reply="问题已解决"
        )
        
        updated_feedback = feedback_service.update_feedback(
            db=db,
            feedback_id=feedback.id,
            update_data=update_data
        )
        
        assert updated_feedback is not None
        assert updated_feedback.status == FeedbackStatus.RESOLVED.value
        assert updated_feedback.admin_reply == "问题已解决"
    
    def test_get_feedback_stats(self, db: Session, feedback_service: FeedbackService, test_user: User):
        """测试获取反馈统计"""
        # 创建测试数据
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        
        feedbacks_data = [
            ("反馈1", FeedbackStatus.PENDING, today),
            ("反馈2", FeedbackStatus.PROCESSING, today),
            ("反馈3", FeedbackStatus.RESOLVED, yesterday),
            ("反馈4", FeedbackStatus.IGNORED, yesterday),
        ]
        
        for content, status, created_at in feedbacks_data:
            feedback = Feedback(
                content=content,
                user_id=test_user.id,
                status=status.value,
                created_at=created_at
            )
            db.add(feedback)
        db.commit()
        
        # 获取统计信息
        stats = feedback_service.get_feedback_stats(db)
        
        assert stats.total_count == 4
        assert stats.pending_count == 1
        assert stats.processing_count == 1
        assert stats.resolved_count == 1
        assert stats.ignored_count == 1
        assert stats.today_count == 2  # 今天创建的反馈数量
    
    def test_get_user_feedbacks(self, db: Session, feedback_service: FeedbackService, test_user: User):
        """测试获取用户反馈列表"""
        # 创建其他用户
        other_user = User(
            username="otheruser",
            nickname="其他用户",
            password_hash="hashed_password",
            role=UserRole.USER.value,
            is_active=True,
            is_verified=True
        )
        db.add(other_user)
        db.commit()
        db.refresh(other_user)
        
        # 创建测试数据
        feedback1 = Feedback(content="用户1的反馈", user_id=test_user.id, status=FeedbackStatus.PENDING.value)
        feedback2 = Feedback(content="用户2的反馈", user_id=other_user.id, status=FeedbackStatus.PENDING.value)
        db.add_all([feedback1, feedback2])
        db.commit()
        
        # 获取特定用户的反馈
        feedbacks, total = feedback_service.get_user_feedbacks(db, test_user.id)
        
        assert total == 1
        assert len(feedbacks) == 1
        assert feedbacks[0].user_id == test_user.id
        assert feedbacks[0].content == "用户1的反馈"
    
    def test_update_nonexistent_feedback(self, db: Session, feedback_service: FeedbackService):
        """测试更新不存在的反馈"""
        update_data = FeedbackUpdate(
            status=FeedbackStatus.RESOLVED,
            admin_reply="测试回复"
        )
        
        result = feedback_service.update_feedback(
            db=db,
            feedback_id=99999,  # 不存在的ID
            update_data=update_data
        )
        
        assert result is None
