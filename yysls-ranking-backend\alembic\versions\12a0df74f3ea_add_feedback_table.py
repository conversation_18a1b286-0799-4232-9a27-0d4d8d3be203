"""add_feedback_table

Revision ID: 12a0df74f3ea
Revises: 006
Create Date: 2025-08-04 15:02:11.725660

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '12a0df74f3ea'
down_revision = '006'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('feedback',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='反馈ID'),
    sa.Column('content', sa.Text(), nullable=False, comment='反馈内容'),
    sa.Column('user_id', sa.Integer(), nullable=True, comment='用户ID，可为空支持匿名反馈'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='反馈状态'),
    sa.Column('admin_reply', sa.Text(), nullable=True, comment='管理员回复'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='用户反馈表',
    mysql_charset='utf8mb4',
    mysql_collate='utf8mb4_unicode_ci',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_feedback_status'), 'feedback', ['status'], unique=False)
    op.create_index(op.f('ix_feedback_user_id'), 'feedback', ['user_id'], unique=False)
    op.add_column('broadcast_messages', sa.Column('display_duration', sa.Integer(), nullable=False, comment='显示时长(秒)'))
    op.add_column('broadcast_messages', sa.Column('display_order', sa.Integer(), nullable=False, comment='显示顺序'))
    op.add_column('broadcast_messages', sa.Column('created_by', sa.Integer(), nullable=False, comment='创建人ID'))
    op.alter_column('broadcast_messages', 'id',
               existing_type=mysql.INTEGER(),
               comment=None,
               existing_comment='消息ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('broadcast_messages', 'message',
               existing_type=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'),
               comment='播报消息',
               existing_comment='消息内容',
               existing_nullable=False)
    op.alter_column('broadcast_messages', 'message_type',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=20),
               type_=sa.String(length=50),
               existing_comment='消息类型',
               existing_nullable=False,
               existing_server_default=sa.text("'info'"))
    op.alter_column('broadcast_messages', 'is_active',
               existing_type=mysql.TINYINT(display_width=1),
               comment='是否启用',
               existing_comment='是否激活',
               existing_nullable=False,
               existing_server_default=sa.text("'1'"))
    op.alter_column('broadcast_messages', 'start_time',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='开始时间',
               existing_nullable=True)
    op.alter_column('broadcast_messages', 'end_time',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='结束时间',
               existing_nullable=True)
    op.alter_column('broadcast_messages', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('broadcast_messages', 'updated_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.drop_index('idx_created_at', table_name='broadcast_messages')
    op.drop_index('idx_end_time', table_name='broadcast_messages')
    op.drop_index('idx_is_active', table_name='broadcast_messages')
    op.drop_index('idx_message_type', table_name='broadcast_messages')
    op.drop_index('idx_start_time', table_name='broadcast_messages')
    op.create_index(op.f('ix_broadcast_messages_id'), 'broadcast_messages', ['id'], unique=False)
    op.create_foreign_key(None, 'broadcast_messages', 'users', ['created_by'], ['id'])
    op.drop_table_comment(
        'broadcast_messages',
        existing_comment='播报消息表',
        schema=None
    )
    op.alter_column('contents', 'id',
               existing_type=mysql.INTEGER(),
               comment=None,
               existing_comment='内容ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('contents', 'content',
               existing_type=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'),
               comment='内容正文',
               existing_comment='内容',
               existing_nullable=True)
    op.alter_column('contents', 'is_published',
               existing_type=mysql.TINYINT(display_width=1),
               comment='发布状态',
               existing_comment='是否已发布',
               existing_nullable=False,
               existing_server_default=sa.text("'0'"))
    op.alter_column('contents', 'publish_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='发布时间',
               existing_nullable=True)
    op.alter_column('contents', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('contents', 'updated_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.drop_table_comment(
        'contents',
        existing_comment='内容表',
        schema=None
    )
    op.alter_column('ranking_details', 'id',
               existing_type=mysql.INTEGER(),
               comment=None,
               existing_comment='明细ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('ranking_details', 'completion_seconds',
               existing_type=mysql.INTEGER(),
               nullable=False,
               comment='完成时间(总秒数)',
               existing_comment='完成时间（秒）')
    op.alter_column('ranking_details', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('ranking_details', 'updated_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.drop_index('idx_completion_seconds', table_name='ranking_details')
    op.drop_index('idx_created_at', table_name='ranking_details')
    op.drop_index('idx_ranking_id', table_name='ranking_details')
    op.create_index(op.f('ix_ranking_details_id'), 'ranking_details', ['id'], unique=False)
    op.drop_constraint('ranking_details_ibfk_1', 'ranking_details', type_='foreignkey')
    op.create_foreign_key(None, 'ranking_details', 'rankings', ['ranking_id'], ['id'])
    op.drop_table_comment(
        'ranking_details',
        existing_comment='榜单明细表',
        schema=None
    )
    op.alter_column('rankings', 'id',
               existing_type=mysql.INTEGER(),
               comment=None,
               existing_comment='榜单ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('rankings', 'name',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=200),
               comment='榜单名称',
               existing_comment='榜单标题',
               existing_nullable=False)
    op.alter_column('rankings', 'period',
               existing_type=mysql.TINYINT(),
               type_=sa.Integer(),
               nullable=False,
               comment='期数')
    op.alter_column('rankings', 'start_time',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               nullable=False,
               existing_comment='开始时间')
    op.alter_column('rankings', 'end_time',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               nullable=False,
               existing_comment='结束时间')
    op.alter_column('rankings', 'total_participants',
               existing_type=mysql.INTEGER(),
               comment='总参与人数',
               existing_comment='当前参与人数',
               existing_nullable=False,
               existing_server_default=sa.text("'0'"))
    op.alter_column('rankings', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('rankings', 'updated_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.drop_index('idx_created_at', table_name='rankings')
    op.drop_index('idx_ranking_type', table_name='rankings')
    op.drop_index('idx_start_time', table_name='rankings')
    op.drop_index('idx_status', table_name='rankings')
    op.create_index(op.f('ix_rankings_id'), 'rankings', ['id'], unique=False)
    op.drop_table_comment(
        'rankings',
        existing_comment='榜单表',
        schema=None
    )
    op.drop_column('rankings', 'description')
    op.drop_index('idx_sponsors_new_created_at', table_name='sponsors')
    op.drop_index('idx_sponsors_new_is_active', table_name='sponsors')
    op.drop_index('idx_sponsors_new_sort_order', table_name='sponsors')
    op.create_index(op.f('ix_sponsors_id'), 'sponsors', ['id'], unique=False)
    op.drop_table_comment(
        'sponsors',
        existing_comment='赞助商表',
        schema=None
    )
    op.alter_column('system_configs', 'name',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=30),
               comment='配置名称',
               existing_nullable=True)
    op.alter_column('system_configs', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('system_configs', 'updated_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.drop_index('config_key', table_name='system_configs')
    op.drop_index('idx_config_key', table_name='system_configs')
    op.drop_index('idx_config_type', table_name='system_configs')
    op.drop_index('idx_is_public', table_name='system_configs')
    op.create_index(op.f('ix_system_configs_config_key'), 'system_configs', ['config_key'], unique=True)
    op.create_index(op.f('ix_system_configs_config_type'), 'system_configs', ['config_type'], unique=False)
    op.create_index(op.f('ix_system_configs_id'), 'system_configs', ['id'], unique=False)
    op.create_index(op.f('ix_system_configs_is_public'), 'system_configs', ['is_public'], unique=False)
    op.drop_table_comment(
        'system_configs',
        existing_comment='系统配置表',
        schema=None
    )
    op.drop_index('email', table_name='users')
    op.drop_index('idx_created_at', table_name='users')
    op.drop_index('idx_email', table_name='users')
    op.drop_index('idx_is_active', table_name='users')
    op.drop_index('idx_role', table_name='users')
    op.drop_index('idx_username', table_name='users')
    op.drop_index('idx_users_user_number', table_name='users')
    op.drop_index('idx_wechat_openid', table_name='users')
    op.drop_index('phone', table_name='users')
    op.drop_index('username', table_name='users')
    op.drop_index('wechat_openid', table_name='users')
    op.drop_index('wechat_unionid', table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_phone'), 'users', ['phone'], unique=True)
    op.create_index(op.f('ix_users_user_number'), 'users', ['user_number'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_wechat_openid'), 'users', ['wechat_openid'], unique=True)
    op.create_index(op.f('ix_users_wechat_unionid'), 'users', ['wechat_unionid'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_wechat_unionid'), table_name='users')
    op.drop_index(op.f('ix_users_wechat_openid'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_user_number'), table_name='users')
    op.drop_index(op.f('ix_users_phone'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index('wechat_unionid', 'users', ['wechat_unionid'], unique=False)
    op.create_index('wechat_openid', 'users', ['wechat_openid'], unique=False)
    op.create_index('username', 'users', ['username'], unique=False)
    op.create_index('phone', 'users', ['phone'], unique=False)
    op.create_index('idx_wechat_openid', 'users', ['wechat_openid'], unique=False)
    op.create_index('idx_users_user_number', 'users', ['user_number'], unique=False)
    op.create_index('idx_username', 'users', ['username'], unique=False)
    op.create_index('idx_role', 'users', ['role'], unique=False)
    op.create_index('idx_is_active', 'users', ['is_active'], unique=False)
    op.create_index('idx_email', 'users', ['email'], unique=False)
    op.create_index('idx_created_at', 'users', ['created_at'], unique=False)
    op.create_index('email', 'users', ['email'], unique=False)
    op.create_table_comment(
        'system_configs',
        '系统配置表',
        existing_comment=None,
        schema=None
    )
    op.drop_index(op.f('ix_system_configs_is_public'), table_name='system_configs')
    op.drop_index(op.f('ix_system_configs_id'), table_name='system_configs')
    op.drop_index(op.f('ix_system_configs_config_type'), table_name='system_configs')
    op.drop_index(op.f('ix_system_configs_config_key'), table_name='system_configs')
    op.create_index('idx_is_public', 'system_configs', ['is_public'], unique=False)
    op.create_index('idx_config_type', 'system_configs', ['config_type'], unique=False)
    op.create_index('idx_config_key', 'system_configs', ['config_key'], unique=False)
    op.create_index('config_key', 'system_configs', ['config_key'], unique=False)
    op.alter_column('system_configs', 'updated_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.alter_column('system_configs', 'created_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('system_configs', 'name',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=30),
               comment=None,
               existing_comment='配置名称',
               existing_nullable=True)
    op.create_table_comment(
        'sponsors',
        '赞助商表',
        existing_comment=None,
        schema=None
    )
    op.drop_index(op.f('ix_sponsors_id'), table_name='sponsors')
    op.create_index('idx_sponsors_new_sort_order', 'sponsors', ['sort_order'], unique=False)
    op.create_index('idx_sponsors_new_is_active', 'sponsors', ['is_active'], unique=False)
    op.create_index('idx_sponsors_new_created_at', 'sponsors', ['created_at'], unique=False)
    op.add_column('rankings', sa.Column('description', mysql.TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'), nullable=True, comment='榜单描述'))
    op.create_table_comment(
        'rankings',
        '榜单表',
        existing_comment=None,
        schema=None
    )
    op.drop_index(op.f('ix_rankings_id'), table_name='rankings')
    op.create_index('idx_status', 'rankings', ['status'], unique=False)
    op.create_index('idx_start_time', 'rankings', ['start_time'], unique=False)
    op.create_index('idx_ranking_type', 'rankings', ['ranking_type'], unique=False)
    op.create_index('idx_created_at', 'rankings', ['created_at'], unique=False)
    op.alter_column('rankings', 'updated_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.alter_column('rankings', 'created_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('rankings', 'total_participants',
               existing_type=mysql.INTEGER(),
               comment='当前参与人数',
               existing_comment='总参与人数',
               existing_nullable=False,
               existing_server_default=sa.text("'0'"))
    op.alter_column('rankings', 'end_time',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               nullable=True,
               existing_comment='结束时间')
    op.alter_column('rankings', 'start_time',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               nullable=True,
               existing_comment='开始时间')
    op.alter_column('rankings', 'period',
               existing_type=sa.Integer(),
               type_=mysql.TINYINT(),
               nullable=True,
               comment=None,
               existing_comment='期数')
    op.alter_column('rankings', 'name',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=200),
               comment='榜单标题',
               existing_comment='榜单名称',
               existing_nullable=False)
    op.alter_column('rankings', 'id',
               existing_type=mysql.INTEGER(),
               comment='榜单ID',
               existing_nullable=False,
               autoincrement=True)
    op.create_table_comment(
        'ranking_details',
        '榜单明细表',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'ranking_details', type_='foreignkey')
    op.create_foreign_key('ranking_details_ibfk_1', 'ranking_details', 'rankings', ['ranking_id'], ['id'], onupdate='RESTRICT', ondelete='CASCADE')
    op.drop_index(op.f('ix_ranking_details_id'), table_name='ranking_details')
    op.create_index('idx_ranking_id', 'ranking_details', ['ranking_id'], unique=False)
    op.create_index('idx_created_at', 'ranking_details', ['created_at'], unique=False)
    op.create_index('idx_completion_seconds', 'ranking_details', ['completion_seconds'], unique=False)
    op.alter_column('ranking_details', 'updated_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.alter_column('ranking_details', 'created_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('ranking_details', 'completion_seconds',
               existing_type=mysql.INTEGER(),
               nullable=True,
               comment='完成时间（秒）',
               existing_comment='完成时间(总秒数)')
    op.alter_column('ranking_details', 'id',
               existing_type=mysql.INTEGER(),
               comment='明细ID',
               existing_nullable=False,
               autoincrement=True)
    op.create_table_comment(
        'contents',
        '内容表',
        existing_comment=None,
        schema=None
    )
    op.alter_column('contents', 'updated_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.alter_column('contents', 'created_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('contents', 'publish_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='发布时间',
               existing_nullable=True)
    op.alter_column('contents', 'is_published',
               existing_type=mysql.TINYINT(display_width=1),
               comment='是否已发布',
               existing_comment='发布状态',
               existing_nullable=False,
               existing_server_default=sa.text("'0'"))
    op.alter_column('contents', 'content',
               existing_type=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'),
               comment='内容',
               existing_comment='内容正文',
               existing_nullable=True)
    op.alter_column('contents', 'id',
               existing_type=mysql.INTEGER(),
               comment='内容ID',
               existing_nullable=False,
               autoincrement=True)
    op.create_table_comment(
        'broadcast_messages',
        '播报消息表',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'broadcast_messages', type_='foreignkey')
    op.drop_index(op.f('ix_broadcast_messages_id'), table_name='broadcast_messages')
    op.create_index('idx_start_time', 'broadcast_messages', ['start_time'], unique=False)
    op.create_index('idx_message_type', 'broadcast_messages', ['message_type'], unique=False)
    op.create_index('idx_is_active', 'broadcast_messages', ['is_active'], unique=False)
    op.create_index('idx_end_time', 'broadcast_messages', ['end_time'], unique=False)
    op.create_index('idx_created_at', 'broadcast_messages', ['created_at'], unique=False)
    op.alter_column('broadcast_messages', 'updated_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.alter_column('broadcast_messages', 'created_at',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('broadcast_messages', 'end_time',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='结束时间',
               existing_nullable=True)
    op.alter_column('broadcast_messages', 'start_time',
               existing_type=sa.DateTime(),
               type_=mysql.TIMESTAMP(),
               existing_comment='开始时间',
               existing_nullable=True)
    op.alter_column('broadcast_messages', 'is_active',
               existing_type=mysql.TINYINT(display_width=1),
               comment='是否激活',
               existing_comment='是否启用',
               existing_nullable=False,
               existing_server_default=sa.text("'1'"))
    op.alter_column('broadcast_messages', 'message_type',
               existing_type=sa.String(length=50),
               type_=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_unicode_ci', length=20),
               existing_comment='消息类型',
               existing_nullable=False,
               existing_server_default=sa.text("'info'"))
    op.alter_column('broadcast_messages', 'message',
               existing_type=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_unicode_ci'),
               comment='消息内容',
               existing_comment='播报消息',
               existing_nullable=False)
    op.alter_column('broadcast_messages', 'id',
               existing_type=mysql.INTEGER(),
               comment='消息ID',
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('broadcast_messages', 'created_by')
    op.drop_column('broadcast_messages', 'display_order')
    op.drop_column('broadcast_messages', 'display_duration')
    op.drop_index(op.f('ix_feedback_user_id'), table_name='feedback')
    op.drop_index(op.f('ix_feedback_status'), table_name='feedback')
    op.drop_table('feedback')
    # ### end Alembic commands ###
