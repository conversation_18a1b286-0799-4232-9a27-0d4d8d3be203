"""
微信API集成工具

提供微信登录、用户信息获取等功能
"""
import httpx
import logging
from typing import Dict, Any, Optional
from urllib.parse import urlencode

from app.config import settings

logger = logging.getLogger(__name__)


class WeChatAPIError(Exception):
    """微信API错误"""
    def __init__(self, message: str, error_code: Optional[str] = None):
        self.message = message
        self.error_code = error_code
        super().__init__(message)


class WeChatAPI:
    """微信API客户端"""

    # 微信API端点
    # 小程序登录接口
    JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session"

    # OAuth2接口（保留用于其他场景）
    OAUTH2_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token"
    OAUTH2_REFRESH_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/refresh_token"
    OAUTH2_USERINFO_URL = "https://api.weixin.qq.com/sns/userinfo"
    OAUTH2_AUTH_URL = "https://open.weixin.qq.com/connect/oauth2/authorize"
    
    def __init__(self):
        self.app_id = settings.wechat_app_id
        self.app_secret = settings.wechat_app_secret
        self.timeout = 10.0
    
    async def jscode2session(self, js_code: str) -> Dict[str, Any]:
        """
        小程序登录，通过js_code获取session_key和openid

        Args:
            js_code: 小程序调用wx.login()获得的临时登录凭证code

        Returns:
            包含openid、session_key、unionid等信息的字典

        Raises:
            WeChatAPIError: API调用失败时抛出
        """
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "js_code": js_code,
            "grant_type": "authorization_code"
        }

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.JSCODE2SESSION_URL, params=params)
                response.raise_for_status()

                data = response.json()

                # 检查微信API错误
                if "errcode" in data:
                    error_msg = data.get("errmsg", "未知错误")
                    error_code = data.get("errcode")
                    logger.error(f"微信小程序登录失败: {error_code} - {error_msg}")
                    raise WeChatAPIError(f"微信API错误: {error_msg}", str(error_code))

                logger.info(f"成功获取小程序session openid={data.get('openid', 'N/A')}")
                return data

        except httpx.HTTPError as e:
            logger.error(f"微信API HTTP请求失败: {str(e)}")
            raise WeChatAPIError(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"小程序登录异常: {str(e)}")
            raise WeChatAPIError(f"小程序登录失败: {str(e)}")

    async def get_access_token(self, code: str) -> Dict[str, Any]:
        """
        通过授权码获取访问令牌（OAuth2方式，用于公众号等场景）

        Args:
            code: 微信授权码

        Returns:
            包含access_token、openid等信息的字典

        Raises:
            WeChatAPIError: API调用失败时抛出
        """
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "code": code,
            "grant_type": "authorization_code"
        }

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.OAUTH2_ACCESS_TOKEN_URL, params=params)
                response.raise_for_status()

                data = response.json()

                # 检查微信API错误
                if "errcode" in data:
                    error_msg = data.get("errmsg", "未知错误")
                    error_code = data.get("errcode")
                    logger.error(f"微信获取访问令牌失败: {error_code} - {error_msg}")
                    raise WeChatAPIError(f"微信API错误: {error_msg}", str(error_code))

                logger.info(f"成功获取微信访问令牌 openid={data.get('openid', 'N/A')}")
                return data

        except httpx.HTTPError as e:
            logger.error(f"微信API HTTP请求失败: {str(e)}")
            raise WeChatAPIError(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取微信访问令牌异常: {str(e)}")
            raise WeChatAPIError(f"获取访问令牌失败: {str(e)}")
    
    async def get_user_info(self, access_token: str, openid: str) -> Dict[str, Any]:
        """
        获取用户信息
        
        Args:
            access_token: 访问令牌
            openid: 用户openid
            
        Returns:
            用户信息字典
            
        Raises:
            WeChatAPIError: API调用失败时抛出
        """
        params = {
            "access_token": access_token,
            "openid": openid,
            "lang": "zh_CN"
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.OAUTH2_USERINFO_URL, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # 检查微信API错误
                if "errcode" in data:
                    error_msg = data.get("errmsg", "未知错误")
                    error_code = data.get("errcode")
                    logger.error(f"微信获取用户信息失败: {error_code} - {error_msg}")
                    raise WeChatAPIError(f"微信API错误: {error_msg}", str(error_code))
                
                logger.info(f"成功获取微信用户信息 openid={openid}")
                return data
                
        except httpx.HTTPError as e:
            logger.error(f"微信API HTTP请求失败: {str(e)}")
            raise WeChatAPIError(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取微信用户信息异常: {str(e)}")
            raise WeChatAPIError(f"获取用户信息失败: {str(e)}")
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            新的令牌信息
            
        Raises:
            WeChatAPIError: API调用失败时抛出
        """
        params = {
            "appid": self.app_id,
            "grant_type": "refresh_token",
            "refresh_token": refresh_token
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(self.OAUTH2_REFRESH_TOKEN_URL, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # 检查微信API错误
                if "errcode" in data:
                    error_msg = data.get("errmsg", "未知错误")
                    error_code = data.get("errcode")
                    logger.error(f"微信刷新令牌失败: {error_code} - {error_msg}")
                    raise WeChatAPIError(f"微信API错误: {error_msg}", str(error_code))
                
                logger.info("成功刷新微信访问令牌")
                return data
                
        except httpx.HTTPError as e:
            logger.error(f"微信API HTTP请求失败: {str(e)}")
            raise WeChatAPIError(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"刷新微信访问令牌异常: {str(e)}")
            raise WeChatAPIError(f"刷新令牌失败: {str(e)}")
    
    def get_oauth_url(self, redirect_uri: str, scope: str = "snsapi_userinfo", state: str = "") -> str:
        """
        生成微信OAuth授权URL
        
        Args:
            redirect_uri: 回调地址
            scope: 授权作用域
            state: 状态参数
            
        Returns:
            授权URL
        """
        params = {
            "appid": self.app_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": scope,
            "state": state
        }
        
        url = f"{self.OAUTH2_AUTH_URL}?{urlencode(params)}#wechat_redirect"
        logger.info(f"生成微信OAuth授权URL: {url}")
        return url
    
    async def miniprogram_login(self, js_code: str) -> Dict[str, Any]:
        """
        小程序登录流程

        Args:
            js_code: 小程序调用wx.login()获得的临时登录凭证code

        Returns:
            包含用户信息的字典

        Raises:
            WeChatAPIError: 登录失败时抛出
        """
        try:
            # 获取session信息
            session_data = await self.jscode2session(js_code)

            openid = session_data.get("openid")
            session_key = session_data.get("session_key")

            if not openid or not session_key:
                raise WeChatAPIError("获取session信息失败")

            logger.info(f"小程序登录成功 openid={openid}")
            return session_data

        except WeChatAPIError:
            raise
        except Exception as e:
            logger.error(f"小程序登录流程异常: {str(e)}")
            raise WeChatAPIError(f"小程序登录失败: {str(e)}")

    async def login_with_code(self, code: str) -> Dict[str, Any]:
        """
        使用授权码完成微信登录流程（OAuth2方式，用于公众号等场景）

        Args:
            code: 微信授权码

        Returns:
            包含用户信息的字典

        Raises:
            WeChatAPIError: 登录失败时抛出
        """
        try:
            # 1. 获取访问令牌
            token_data = await self.get_access_token(code)

            access_token = token_data.get("access_token")
            openid = token_data.get("openid")

            if not access_token or not openid:
                raise WeChatAPIError("获取访问令牌失败")

            # 2. 获取用户信息
            user_info = await self.get_user_info(access_token, openid)

            # 3. 合并令牌信息和用户信息
            result = {
                **token_data,
                **user_info
            }

            logger.info(f"微信登录成功 openid={openid}")
            return result

        except WeChatAPIError:
            raise
        except Exception as e:
            logger.error(f"微信登录流程异常: {str(e)}")
            raise WeChatAPIError(f"微信登录失败: {str(e)}")


class WeChatUserInfo:
    """微信用户信息处理器"""
    
    @staticmethod
    def normalize_user_info(wechat_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化微信用户信息

        Args:
            wechat_data: 微信API返回的用户数据

        Returns:
            标准化的用户信息
        """
        return {
            "openid": wechat_data.get("openid"),
            "unionid": wechat_data.get("unionid"),
            "session_key": wechat_data.get("session_key"),  # 小程序session_key
            "nickname": wechat_data.get("nickname", ""),
            "avatar_url": wechat_data.get("headimgurl", ""),
            "gender": wechat_data.get("sex", 0),  # 0-未知, 1-男, 2-女
            "country": wechat_data.get("country", ""),
            "province": wechat_data.get("province", ""),
            "city": wechat_data.get("city", ""),
            "language": wechat_data.get("language", "zh_CN"),
            "access_token": wechat_data.get("access_token"),
            "refresh_token": wechat_data.get("refresh_token"),
            "expires_in": wechat_data.get("expires_in", 7200),
            "scope": wechat_data.get("scope", "")
        }

    @staticmethod
    def normalize_miniprogram_info(session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化小程序用户信息

        Args:
            session_data: 小程序jscode2session返回的数据

        Returns:
            标准化的用户信息
        """
        return {
            "openid": session_data.get("openid"),
            "unionid": session_data.get("unionid"),
            "session_key": session_data.get("session_key"),
            "nickname": "少冬瓜",  # 小程序需要用户授权才能获取昵称
            "avatar_url": "https://yysls.sappan.top/static/e.jpg",  # 小程序需要用户授权才能获取头像
            "gender": 0,
            "country": "",
            "province": "",
            "city": "",
            "language": "zh_CN"
        }
    
    @staticmethod
    def extract_user_profile(user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        从微信用户信息中提取用户档案
        
        Args:
            user_info: 标准化的用户信息
            
        Returns:
            用户档案信息
        """
        return {
            "username": f"wx_{user_info.get('openid', '')[:16]}",  # 生成用户名
            "nickname": user_info.get("nickname", "微信用户"),
            "avatar_url": user_info.get("avatar_url", ""),
            "wechat_openid": user_info.get("openid"),
            "wechat_unionid": user_info.get("unionid"),
            "wechat_info": {
                "gender": user_info.get("gender", 0),
                "country": user_info.get("country", ""),
                "province": user_info.get("province", ""),
                "city": user_info.get("city", ""),
                "language": user_info.get("language", "zh_CN")
            }
        }


# 全局微信API客户端实例
wechat_api = WeChatAPI()

# 导出
__all__ = [
    "WeChatAPI",
    "WeChatAPIError", 
    "WeChatUserInfo",
    "wechat_api"
]
