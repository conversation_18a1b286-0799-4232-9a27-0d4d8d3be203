"""
用户反馈模型
"""
from datetime import datetime
from enum import Enum

from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class FeedbackStatus(str, Enum):
    """反馈状态枚举"""
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    RESOLVED = "resolved"    # 已处理
    IGNORED = "ignored"      # 已忽略


class Feedback(Base):
    """用户反馈表"""
    __tablename__ = "feedback"
    __table_args__ = (
        {
            'mysql_engine': 'InnoDB',
            'mysql_charset': 'utf8mb4',
            'mysql_collate': 'utf8mb4_unicode_ci',
            'comment': '用户反馈表'
        }
    )

    id = Column(Integer, primary_key=True, autoincrement=True, comment="反馈ID")
    
    # 反馈内容
    content = Column(Text, nullable=False, comment="反馈内容")
    
    # 用户信息（可选，支持匿名反馈）
    user_id = Column(
        Integer, 
        ForeignKey("users.id", ondelete="SET NULL"), 
        nullable=True, 
        index=True, 
        comment="用户ID，可为空支持匿名反馈"
    )
    
    # 反馈状态
    status = Column(
        String(20), 
        default=FeedbackStatus.PENDING.value, 
        nullable=False, 
        index=True,
        comment="反馈状态"
    )
    
    # 管理员回复
    admin_reply = Column(Text, nullable=True, comment="管理员回复")
    
    # 时间戳
    created_at = Column(
        DateTime, 
        default=func.now(), 
        nullable=False, 
        comment="创建时间"
    )
    updated_at = Column(
        DateTime, 
        default=func.now(), 
        onupdate=func.now(), 
        nullable=False, 
        comment="更新时间"
    )
    
    # 关联关系
    user = relationship("User", back_populates="feedbacks", lazy="select")

    def __repr__(self):
        return f"<Feedback(id={self.id}, status={self.status}, user_id={self.user_id})>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "content": self.content,
            "user_id": self.user_id,
            "status": self.status,
            "admin_reply": self.admin_reply,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
