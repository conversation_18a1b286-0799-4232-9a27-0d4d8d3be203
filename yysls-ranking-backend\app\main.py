"""
FastAPI应用主入口
"""
import logging
import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles

from app.config import settings
from app.api.v1.api import api_router


def setup_logging():
    """设置日志配置"""
    try:
        # 创建日志目录
        log_dir = os.path.dirname(settings.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # 检查是否已经配置过日志
        root_logger = logging.getLogger()
        if root_logger.handlers:
            return

        # 配置日志格式
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        date_format = "%Y-%m-%d %H:%M:%S"

        # 创建处理器
        handlers = [logging.StreamHandler()]

        # 只在文件可写时添加文件处理器
        try:
            file_handler = logging.FileHandler(settings.log_file, encoding='utf-8')
            handlers.append(file_handler)
        except (OSError, PermissionError):
            # 如果无法创建文件处理器，只使用控制台输出
            pass

        # 配置根日志器
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper(), logging.INFO),
            format=log_format,
            datefmt=date_format,
            handlers=handlers,
            force=True  # 强制重新配置
        )

        # 设置第三方库的日志级别
        logging.getLogger("uvicorn").setLevel(logging.INFO)
        logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
        logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)

    except Exception as e:
        # 如果日志配置失败，使用基本配置
        logging.basicConfig(level=logging.INFO)
        logging.getLogger(__name__).warning(f"日志配置失败，使用默认配置: {e}")


def create_application() -> FastAPI:
    """创建FastAPI应用实例"""

    # 设置日志配置
    setup_logging()

    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="燕友圈榜单系统后端API服务",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # 添加CORS中间件
    if settings.allowed_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.allowed_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # 添加可信主机中间件
    app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])
    
    # 挂载静态文件目录
    if os.path.exists(settings.static_dir_absolute):
        app.mount(
            settings.static_url_path,
            StaticFiles(directory=settings.static_dir_absolute),
            name="static"
        )
        logging.getLogger(__name__).info(f"静态文件服务已启用: {settings.static_url_path} -> {settings.static_dir_absolute}")

    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    @app.get("/")
    async def root():
        """根路径健康检查"""
        return {
            "message": f"欢迎使用{settings.app_name}",
            "version": settings.app_version,
            "status": "running"
        }
    
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {"status": "healthy"}
    
    return app


# 创建应用实例
app = create_application()
